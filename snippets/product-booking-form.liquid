{% comment %}
  Renders a booking form for tour products
  
  Accepts:
  - product: {Object} Product object
  
  Usage:
  {% render 'product-booking-form', product: product %}
{% endcomment %}

<div class="booking-form-wrapper">
  <div class="booking-form-container">
    <div class="booking-form-header">
      <div class="booking-price">
        <span class="price-label">{{ 'products.booking.from' | t | default: 'from' }}</span>
        <span class="price-amount">{{ product.price | money }}</span>
      </div>
    </div>
    
    <form class="booking-form" id="booking-form-{{ product.id }}">
      <!-- Date Selection -->
      <div class="booking-field">
        <label for="booking-date" class="booking-label">{{ 'products.booking.date' | t | default: 'Date' }}</label>
        <input 
          type="date" 
          id="booking-date" 
          name="booking_date" 
          class="booking-input"
          required
          min="{{ 'now' | date: '%Y-%m-%d' }}"
        >
      </div>
      
      <!-- Adults Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.adults' | t | default: 'Adults' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="adults">-</button>
          <input 
            type="number" 
            id="booking-adults" 
            name="adults" 
            class="quantity-input" 
            value="2" 
            min="1" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="adults">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_18_plus' | t | default: 'Age 18+' }}</span>
      </div>
      
      <!-- Children Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.children' | t | default: 'Children' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="children">-</button>
          <input 
            type="number" 
            id="booking-children" 
            name="children" 
            class="quantity-input" 
            value="0" 
            min="0" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="children">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_6_17' | t | default: 'Age 6-17' }}</span>
      </div>
      
      <!-- Infants Counter -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.infants' | t | default: 'Infants' }}</label>
        <div class="quantity-selector">
          <button type="button" class="quantity-btn quantity-minus" data-target="infants">-</button>
          <input 
            type="number" 
            id="booking-infants" 
            name="infants" 
            class="quantity-input" 
            value="0" 
            min="0" 
            max="4"
            readonly
          >
          <button type="button" class="quantity-btn quantity-plus" data-target="infants">+</button>
        </div>
        <span class="age-info">{{ 'products.booking.age_0_5' | t | default: 'Age 0-5' }}</span>
      </div>
      
      <!-- Guest Names -->
      <div class="booking-field guest-names" id="guest-names-container">
        <label class="booking-label">{{ 'products.booking.guest_names' | t | default: 'Guest names' }} *</label>
        <div class="guest-name-inputs">
          <div class="guest-name-row">
            <select class="guest-title-select">
              <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
              <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
              <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
            </select>
            <input 
              type="text" 
              class="guest-name-input" 
              placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
              required
            >
          </div>
          <div class="guest-name-row">
            <select class="guest-title-select">
              <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
              <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
              <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
            </select>
            <input 
              type="text" 
              class="guest-name-input" 
              placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
              required
            >
          </div>
        </div>
      </div>
      
      <!-- Package Selection -->
      <div class="booking-field">
        <label class="booking-label">{{ 'products.booking.package' | t | default: 'Package' }}</label>
        <select class="booking-select" name="package">
          <option value="standard">{{ 'products.booking.standard_package' | t | default: 'Standard Package' }}</option>
          <option value="premium">{{ 'products.booking.premium_package' | t | default: 'Premium Package' }}</option>
          <option value="luxury">{{ 'products.booking.luxury_package' | t | default: 'Luxury Package' }}</option>
        </select>
      </div>
      
      <!-- Submit Button -->
      <button type="submit" class="booking-submit-btn">
        {{ 'products.booking.book_now' | t | default: 'Book Now' }}
      </button>
      
      <!-- Organized by -->
      <div class="booking-footer">
        <span class="organized-by">{{ 'products.booking.organized_by' | t | default: 'Organized by' }}</span>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('booking-form-{{ product.id }}');
  if (!form) return;
  
  // Quantity controls
  const quantityBtns = form.querySelectorAll('.quantity-btn');
  quantityBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const target = this.dataset.target;
      const input = form.querySelector(`#booking-${target}`);
      const isPlus = this.classList.contains('quantity-plus');
      const currentValue = parseInt(input.value);
      const max = parseInt(input.max);
      const min = parseInt(input.min);
      
      let newValue = currentValue;
      if (isPlus && currentValue < max) {
        newValue = currentValue + 1;
      } else if (!isPlus && currentValue > min) {
        newValue = currentValue - 1;
      }
      
      input.value = newValue;
      updateGuestNames();
      checkMaxGuests();
    });
  });
  
  // Update guest name fields based on total guests
  function updateGuestNames() {
    const adults = parseInt(form.querySelector('#booking-adults').value);
    const children = parseInt(form.querySelector('#booking-children').value);
    const totalGuests = adults + children;
    
    const container = form.querySelector('.guest-name-inputs');
    const existingRows = container.querySelectorAll('.guest-name-row');
    
    // Remove excess rows
    for (let i = totalGuests; i < existingRows.length; i++) {
      existingRows[i].remove();
    }
    
    // Add missing rows
    for (let i = existingRows.length; i < totalGuests; i++) {
      const row = document.createElement('div');
      row.className = 'guest-name-row';
      row.innerHTML = `
        <select class="guest-title-select">
          <option value="Mr">{{ 'products.booking.mr' | t | default: 'Mr' }}</option>
          <option value="Ms">{{ 'products.booking.ms' | t | default: 'Ms' }}</option>
          <option value="Mrs">{{ 'products.booking.mrs' | t | default: 'Mrs' }}</option>
        </select>
        <input 
          type="text" 
          class="guest-name-input" 
          placeholder="{{ 'products.booking.guest_name_placeholder' | t | default: 'Guest name' }}"
          required
        >
      `;
      container.appendChild(row);
    }
  }
  
  // Check if total guests exceed 4
  function checkMaxGuests() {
    const adults = parseInt(form.querySelector('#booking-adults').value);
    const children = parseInt(form.querySelector('#booking-children').value);
    const infants = parseInt(form.querySelector('#booking-infants').value);
    const total = adults + children + infants;
    
    // Disable plus buttons if total would exceed 4
    const plusBtns = form.querySelectorAll('.quantity-plus');
    plusBtns.forEach(btn => {
      const target = btn.dataset.target;
      const input = form.querySelector(`#booking-${target}`);
      const currentValue = parseInt(input.value);
      
      if (total >= 4 && target !== 'infants') {
        btn.disabled = true;
      } else if (total >= 4 && target === 'infants' && currentValue === 0) {
        btn.disabled = true;
      } else {
        btn.disabled = false;
      }
    });
  }
  
  // Initialize
  updateGuestNames();
  checkMaxGuests();
  
  // Form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const bookingData = {
      product_id: {{ product.id }},
      date: formData.get('booking_date'),
      adults: formData.get('adults'),
      children: formData.get('children'),
      infants: formData.get('infants'),
      package: formData.get('package'),
      guests: []
    };
    
    // Collect guest names
    const guestInputs = this.querySelectorAll('.guest-name-input');
    const guestTitles = this.querySelectorAll('.guest-title-select');
    
    for (let i = 0; i < guestInputs.length; i++) {
      if (guestInputs[i].value.trim()) {
        bookingData.guests.push({
          title: guestTitles[i].value,
          name: guestInputs[i].value.trim()
        });
      }
    }
    
    console.log('Booking data:', bookingData);
    
    // Here you would typically send the data to your booking system
    // For now, we'll just show an alert
    alert('{{ "products.booking.booking_submitted" | t | default: "Booking request submitted! We will contact you soon." }}');
  });
});
</script>
