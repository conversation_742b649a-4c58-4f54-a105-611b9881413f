/* Booking Form Styles */
.product-with-booking {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
  align-items: start;
}

.product__booking-wrapper {
  position: sticky;
  top: 1.5rem;
  height: fit-content;
  align-self: start;
}

.booking-form-wrapper {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  width: 100%;
  max-width: 380px;
  margin: 0 auto;
}

.booking-form-container {
  padding: 2rem 1.75rem;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.booking-form-header {
  margin-bottom: 2rem;
  text-align: center;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.booking-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.price-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.price-amount {
  font-size: 2.25rem;
  font-weight: 800;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.booking-field {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.booking-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0;
}

.booking-input,
.booking-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1.5px solid #e5e7eb;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  background: #fafafa;
  transition: all 0.2s ease;
}

.booking-input:focus,
.booking-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Quantity Selector */
.quantity-selector {
  display: flex;
  align-items: center;
  border: 1.5px solid #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  background: #fafafa;
  transition: all 0.2s ease;
}

.quantity-selector:focus-within {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quantity-btn {
  background: transparent;
  border: none;
  padding: 0.875rem 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  border: none;
  text-align: center;
  font-weight: 700;
  font-size: 1rem;
  padding: 0.875rem 0.5rem;
  width: 70px;
  background: transparent;
  color: #1f2937;
}

.quantity-input:focus {
  outline: none;
  box-shadow: none;
}

.age-info {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Guest Names */
.guest-names {
  margin-top: 0.5rem;
}

.guest-name-inputs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.guest-name-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.guest-title-select {
  flex: 0 0 auto;
  width: 70px;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.guest-name-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.guest-name-input:focus,
.guest-title-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Submit Button */
.booking-submit-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 1.125rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: 100%;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.booking-submit-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.booking-submit-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

/* Footer */
.booking-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.organized-by {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2rem;
  }
  
  .booking-form-wrapper {
    max-width: 100%;
    margin-left: 0;
  }
}

@media screen and (max-width: 990px) {
  .product {
    grid-template-columns: 1fr !important;
  }
  
  .product__booking-wrapper {
    order: 3;
    margin-top: 2rem;
  }
  
  .booking-form-wrapper {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 750px) {
  .booking-form-container {
    padding: 1rem;
  }
  
  .price-amount {
    font-size: 1.75rem;
  }
  
  .guest-name-row {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .guest-title-select {
    width: 100%;
  }
}

/* Mobile Layout */
@media screen and (max-width: 749px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2.5rem;
  }

  .booking-form-wrapper {
    max-width: 100%;
    margin: 0;
  }

  .booking-form-container {
    padding: 1.5rem;
  }
}

/* Responsive Layout */
@media screen and (min-width: 750px) {
  .product-with-booking {
    grid-template-columns: 1fr 380px;
    gap: 2.5rem;
    align-items: start;
  }

  .booking-form-wrapper {
    max-width: 380px;
  }
}

@media screen and (min-width: 990px) {
  .product-with-booking {
    grid-template-columns: 1fr 400px;
    gap: 3rem;
  }

  .booking-form-wrapper {
    max-width: 400px;
  }
}

@media screen and (min-width: 1200px) {
  .product-with-booking {
    grid-template-columns: 1fr 420px;
    gap: 3.5rem;
  }

  .booking-form-wrapper {
    max-width: 420px;
  }
}
