/* Booking Form Styles */
.product__booking-wrapper {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.booking-form-wrapper {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  max-width: 350px;
  margin-left: auto;
}

.booking-form-container {
  padding: 1.5rem;
}

.booking-form-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.booking-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.price-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.price-amount {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.booking-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.booking-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.booking-input,
.booking-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.booking-input:focus,
.booking-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Quantity Selector */
.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.quantity-btn {
  background: #f9fafb;
  border: none;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  border: none;
  text-align: center;
  font-weight: 600;
  padding: 0.75rem 0.5rem;
  width: 60px;
  background: white;
}

.quantity-input:focus {
  outline: none;
  box-shadow: none;
}

.age-info {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Guest Names */
.guest-names {
  margin-top: 0.5rem;
}

.guest-name-inputs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.guest-name-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.guest-title-select {
  flex: 0 0 auto;
  width: 70px;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.guest-name-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.guest-name-input:focus,
.guest-title-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Submit Button */
.booking-submit-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.booking-submit-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.booking-submit-btn:active {
  transform: translateY(0);
}

/* Footer */
.booking-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.organized-by {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2rem;
  }
  
  .booking-form-wrapper {
    max-width: 100%;
    margin-left: 0;
  }
}

@media screen and (max-width: 990px) {
  .product {
    grid-template-columns: 1fr !important;
  }
  
  .product__booking-wrapper {
    order: 3;
    margin-top: 2rem;
  }
  
  .booking-form-wrapper {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 750px) {
  .booking-form-container {
    padding: 1rem;
  }
  
  .price-amount {
    font-size: 1.75rem;
  }
  
  .guest-name-row {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .guest-title-select {
    width: 100%;
  }
}

/* Mobile Layout */
@media screen and (max-width: 749px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2rem;
  }

  .booking-form-wrapper {
    max-width: 100%;
    margin: 0;
  }
}

/* Grid Layout Adjustments */
@media screen and (min-width: 750px) {
  .product.grid--3-col-tablet {
    display: grid !important;
    grid-template-columns: 1fr 1fr 320px !important;
    gap: 1.5rem !important;
  }

  .product.grid--3-col-tablet .grid__item {
    width: auto !important;
    max-width: none !important;
  }

  .product__media-wrapper {
    grid-column: 1;
  }

  .product__info-wrapper {
    grid-column: 2;
  }

  .product__booking-wrapper {
    grid-column: 3;
  }

  .booking-form-wrapper {
    max-width: 100%;
  }
}

@media screen and (min-width: 990px) {
  .product.grid--3-col-tablet {
    grid-template-columns: 1fr 1fr 350px !important;
    gap: 2rem !important;
  }

  .booking-form-wrapper {
    max-width: 350px;
  }
}

@media screen and (min-width: 1200px) {
  .product.grid--3-col-tablet {
    grid-template-columns: 1.2fr 1fr 380px !important;
    gap: 3rem !important;
  }

  .booking-form-wrapper {
    max-width: 380px;
  }
}
